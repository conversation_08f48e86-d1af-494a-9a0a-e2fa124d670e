C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\deps\libtree_sitter-47b04f0d78427920.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\../src/parser.h C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out/stdlib-symbols.txt

C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\deps\libtree_sitter-47b04f0d78427920.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\../src/parser.h C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out/stdlib-symbols.txt

C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\deps\tree_sitter-47b04f0d78427920.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\../src/parser.h C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out/stdlib-symbols.txt

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\ffi.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\util.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./README.md:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\./bindings.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\binding_rust\../src/parser.h:
C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out/stdlib-symbols.txt:

# env-dep:OUT_DIR=C:\\Users\\<USER>\\Downloads\\codex-main\\codex-main\\codex-rs\\target\\debug\\build\\tree-sitter-30cb5986fa85ff96\\out
