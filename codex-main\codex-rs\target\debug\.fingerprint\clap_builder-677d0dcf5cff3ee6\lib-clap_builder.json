{"rustc": 10895048813736897673, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\", \"wrap_help\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 11439587820120798860, "path": 12719279374914715567, "deps": [[4858255257716900954, "anstyle", false, 10596089639017151333], [6702271452560637055, "terminal_size", false, 8669709051698522557], [11166530783118767604, "strsim", false, 10999698549243129185], [12553266436076736472, "clap_lex", false, 11381998592279325555], [13237942454122161292, "anstream", false, 15818592050244100825]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-677d0dcf5cff3ee6\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}