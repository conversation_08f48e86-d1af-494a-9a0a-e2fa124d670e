# Codex Configuration for Deepseek
model = "deepseek-chat"
model_provider = "deepseek"

# Deepseek model provider configuration
[model_providers.deepseek]
name = "Deepseek"
base_url = "https://api.deepseek.com/v1"
env_key = "DEEPSEEK_API_KEY"
wire_api = "chat"

# Optional: Set approval policy (you can change this as needed)
# approval_policy = "unless-allow-listed"  # Default: asks for approval for non-safe commands
# approval_policy = "on-failure"           # Only asks for approval if command fails
# approval_policy = "never"                # Never asks for approval

# Optional: Sandbox permissions (you can customize these)
sandbox_permissions = [
    "disk-full-read-access",
    "disk-write-cwd"
]
