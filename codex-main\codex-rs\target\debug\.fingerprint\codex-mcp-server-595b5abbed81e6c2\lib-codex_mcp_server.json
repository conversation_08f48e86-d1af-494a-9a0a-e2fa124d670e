{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2900132144763791395, "profile": 5744688509485938363, "path": 16897057083638320172, "deps": [[2640029339647746139, "codex_core", false, 18384296859478425874], [5138218615291878843, "tokio", false, 11048069128648368332], [6007475091907623661, "toml", false, 1285799993208996568], [6913375703034175521, "schemars", false, 7666037088426628092], [6945861034636732475, "codex_linux_sandbox", false, 6744443309091125485], [7473541655561658027, "mcp_types", false, 10932907881580379939], [8606274917505247608, "tracing", false, 17435560365704135615], [9689903380558560274, "serde", false, 11995195597866155237], [13625485746686963219, "anyhow", false, 8456007207642181901], [15367738274754116744, "serde_json", false, 4504254118857295621], [16230660778393187092, "tracing_subscriber", false, 17006419781542660163]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\codex-mcp-server-595b5abbed81e6c2\\dep-lib-codex_mcp_server", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}