{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 14104341546569349372, "profile": 5744688509485938363, "path": 609103451855213669, "deps": [[404480154280681248, "owo_colors", false, 1740028806240311315], [2640029339647746139, "codex_core", false, 18384296859478425874], [5138218615291878843, "tokio", false, 11048069128648368332], [6547980334806251551, "chrono", false, 987245776564464354], [6945861034636732475, "codex_linux_sandbox", false, 6744443309091125485], [7473541655561658027, "mcp_types", false, 10932907881580379939], [8410525223747752176, "shlex", false, 8724222612176381221], [8532194250340874152, "codex_common", false, 6881562616138974258], [8606274917505247608, "tracing", false, 17435560365704135615], [9236523634534978874, "clap", false, 1015521609604326021], [13625485746686963219, "anyhow", false, 8456007207642181901], [15367738274754116744, "serde_json", false, 4504254118857295621], [16230660778393187092, "tracing_subscriber", false, 17006419781542660163]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\codex-exec-8a258a99ba3a9855\\dep-lib-codex_exec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}