{"rustc": 10895048813736897673, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 13352861347181440008, "deps": [[40386456601120721, "percent_encoding", false, 886669258012938769], [95042085696191081, "ipnet", false, 4195365197424840427], [784494742817713399, "tower_service", false, 12194381386908705868], [1906322745568073236, "pin_project_lite", false, 610365361585168044], [2517136641825875337, "sync_wrapper", false, 1320807216035105612], [3150220818285335163, "url", false, 9372202597953212138], [3722963349756955755, "once_cell", false, 9043037951457003543], [5138218615291878843, "tokio", false, 11048069128648368332], [5695049318159433696, "tower", false, 7539948137539241014], [5986029879202738730, "log", false, 5323037880113620952], [7620660491849607393, "futures_core", false, 11534418981557732287], [8479747168288191681, "tokio_util", false, 16799140994052001887], [9010263965687315507, "http", false, 10362299005077602096], [9689903380558560274, "serde", false, 11995195597866155237], [10229185211513642314, "mime", false, 15571436419703715649], [10595802073777078462, "hyper_util", false, 1415168623023942876], [10629569228670356391, "futures_util", false, 9204028377224346458], [11957360342995674422, "hyper", false, 7801623927330710511], [12186126227181294540, "tokio_native_tls", false, 370588228630805025], [13077212702700853852, "base64", false, 11803448998363212950], [13868379202103418305, "h2", false, 1518601631482478446], [14084095096285906100, "http_body", false, 1483471339105641497], [14564311161534545801, "encoding_rs", false, 16318740129223920315], [15032952994102373905, "rustls_pemfile", false, 13222749874989261584], [15367738274754116744, "serde_json", false, 4504254118857295621], [15697835491348449269, "windows_registry", false, 3611199440628262794], [16066129441945555748, "bytes", false, 4172385814101974191], [16542808166767769916, "serde_urlencoded", false, 4359038749284893826], [16785601910559813697, "native_tls_crate", false, 10757716860217034215], [16900715236047033623, "http_body_util", false, 15283061291957253794], [18273243456331255970, "hyper_tls", false, 627125093642872195]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-27aa6e6cc08d9434\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}