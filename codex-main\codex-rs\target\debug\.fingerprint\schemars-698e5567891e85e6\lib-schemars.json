{"rustc": 10895048813736897673, "features": "[\"default\", \"derive\", \"schemars_derive\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 15657897354478470176, "path": 8494104047910679726, "deps": [[6913375703034175521, "build_script_build", false, 13303383194727614390], [9122563107207267705, "dyn_clone", false, 16282494531925756681], [9689903380558560274, "serde", false, 11995195597866155237], [15367738274754116744, "serde_json", false, 4504254118857295621], [16071897500792579091, "schemars_derive", false, 17927319031563676411]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-698e5567891e85e6\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}