{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 16735142618572465264, "profile": 5744688509485938363, "path": 3563271652587354566, "deps": [[1532211407368564098, "codex_mcp_server", false, 6031354390795992940], [2496773775253438997, "codex_tui", false, 3353512867716706882], [2640029339647746139, "codex_core", false, 18384296859478425874], [5138218615291878843, "tokio", false, 11048069128648368332], [5344309813848860439, "codex_login", false, 10287449665407664315], [6945861034636732475, "codex_linux_sandbox", false, 6744443309091125485], [8532194250340874152, "codex_common", false, 6881562616138974258], [8606274917505247608, "tracing", false, 17435560365704135615], [9236523634534978874, "clap", false, 1015521609604326021], [13625485746686963219, "anyhow", false, 8456007207642181901], [15367738274754116744, "serde_json", false, 4504254118857295621], [16230660778393187092, "tracing_subscriber", false, 17006419781542660163], [16406044557367419809, "codex_exec", false, 11445700436149889295]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\codex-cli-17491cd15d95ab84\\dep-lib-codex_cli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}