{"rustc": 10895048813736897673, "features": "[\"default\", \"std\"]", "declared_features": "[\"bindgen\", \"default\", \"std\", \"wasm\", \"wasmtime-c-api\"]", "target": 625726317299402132, "profile": 724985202928215787, "path": 11976281167743052812, "deps": [[6343042414849818538, "tree_sitter_language", false, 11252065457577301554], [9235208004366183979, "streaming_iterator", false, 16122437522315651164], [9408802513701742484, "regex_syntax", false, 650226483901483026], [9451456094439810778, "regex", false, 16840951685453692718], [15235952884839748695, "build_script_build", false, 14915835406643101291]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tree-sitter-47b04f0d78427920\\dep-lib-tree_sitter", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}