cargo:rerun-if-env-changed=CARGO_FEATURE_WASM
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\alloc.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\alloc.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\array.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\atomic.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\clock.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\error_costs.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\get_changed_ranges.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\get_changed_ranges.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\host.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\language.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\language.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\length.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\lexer.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\lexer.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\lib.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\node.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\parser.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\parser.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\point.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\portable
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\query.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\reduce_action.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\reusable_node.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\stack.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\stack.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\subtree.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\subtree.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\tree.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\tree.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\tree_cursor.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\tree_cursor.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\ts_assert.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\unicode
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\unicode.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\wasm
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\wasm_store.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\src\wasm_store.h
OUT_DIR = Some(C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\deps;C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\eSpeak NG\;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Go\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.cargo\bin;C:\Program Files\Google\Chrome\Application;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\eSpeak NG\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.lmstudio\bin;c:\users\<USER>\appdata\roaming\python\python312\scripts;c:\users\<USER>\.local\bin;C:\Users\<USER>\OneDrive\Documents\AliveAI-Codex\AliveAI;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
OUT_DIR = Some(C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
lib.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=tree-sitter
cargo:rustc-link-search=native=C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\build\tree-sitter-30cb5986fa85ff96\out
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.25.3\include
