{"rustc": 10895048813736897673, "features": "[\"async-timeout\", \"crate-name\"]", "declared_features": "[\"async-timeout\", \"crate-name\", \"default\"]", "target": 16651690207514469086, "profile": 2225463790103693989, "path": 15594534320190618429, "deps": [[1001858567591969688, "build_script_build", false, 14532167605257782702], [1988483478007900009, "unicode_ident", false, 14776100938691580130], [3060637413840920116, "proc_macro2", false, 138309430727902746], [8978325847672661817, "relative_path", false, 9768482144360262924], [8986759836770526006, "syn", false, 15252795665146367195], [9451456094439810778, "regex", false, 12242938809610900682], [10411997081178400487, "cfg_if", false, 4998151317490550319], [15203748914246919255, "proc_macro_crate", false, 4332877801137032622], [17155886227862585100, "glob", false, 4934832565584503606], [17990358020177143287, "quote", false, 16112963820645638977]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rstest_macros-e2149aabcb016729\\dep-lib-rstest_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}