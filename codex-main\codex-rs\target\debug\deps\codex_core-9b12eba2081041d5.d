C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\deps\libcodex_core-9b12eba2081041d5.rmeta: core\src\lib.rs core\src\chat_completions.rs core\src\client.rs core\src\client_common.rs core\src\codex.rs core\src\codex_wrapper.rs core\src\config.rs core\src\config_profile.rs core\src\config_types.rs core\src\conversation_history.rs core\src\error.rs core\src\exec.rs core\src\exec_env.rs core\src\flags.rs core\src\is_safe_command.rs core\src\mcp_connection_manager.rs core\src\mcp_tool_call.rs core\src\message_history.rs core\src\model_provider_info.rs core\src\models.rs core\src\openai_api_key.rs core\src\openai_tools.rs core\src\project_doc.rs core\src\protocol.rs core\src\rollout.rs core\src\safety.rs core\src\user_notification.rs core\src\util.rs core\src\../prompt.md core\src\seatbelt_base_policy.sbpl

C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\deps\libcodex_core-9b12eba2081041d5.rlib: core\src\lib.rs core\src\chat_completions.rs core\src\client.rs core\src\client_common.rs core\src\codex.rs core\src\codex_wrapper.rs core\src\config.rs core\src\config_profile.rs core\src\config_types.rs core\src\conversation_history.rs core\src\error.rs core\src\exec.rs core\src\exec_env.rs core\src\flags.rs core\src\is_safe_command.rs core\src\mcp_connection_manager.rs core\src\mcp_tool_call.rs core\src\message_history.rs core\src\model_provider_info.rs core\src\models.rs core\src\openai_api_key.rs core\src\openai_tools.rs core\src\project_doc.rs core\src\protocol.rs core\src\rollout.rs core\src\safety.rs core\src\user_notification.rs core\src\util.rs core\src\../prompt.md core\src\seatbelt_base_policy.sbpl

C:\Users\<USER>\Downloads\codex-main\codex-main\codex-rs\target\debug\deps\codex_core-9b12eba2081041d5.d: core\src\lib.rs core\src\chat_completions.rs core\src\client.rs core\src\client_common.rs core\src\codex.rs core\src\codex_wrapper.rs core\src\config.rs core\src\config_profile.rs core\src\config_types.rs core\src\conversation_history.rs core\src\error.rs core\src\exec.rs core\src\exec_env.rs core\src\flags.rs core\src\is_safe_command.rs core\src\mcp_connection_manager.rs core\src\mcp_tool_call.rs core\src\message_history.rs core\src\model_provider_info.rs core\src\models.rs core\src\openai_api_key.rs core\src\openai_tools.rs core\src\project_doc.rs core\src\protocol.rs core\src\rollout.rs core\src\safety.rs core\src\user_notification.rs core\src\util.rs core\src\../prompt.md core\src\seatbelt_base_policy.sbpl

core\src\lib.rs:
core\src\chat_completions.rs:
core\src\client.rs:
core\src\client_common.rs:
core\src\codex.rs:
core\src\codex_wrapper.rs:
core\src\config.rs:
core\src\config_profile.rs:
core\src\config_types.rs:
core\src\conversation_history.rs:
core\src\error.rs:
core\src\exec.rs:
core\src\exec_env.rs:
core\src\flags.rs:
core\src\is_safe_command.rs:
core\src\mcp_connection_manager.rs:
core\src\mcp_tool_call.rs:
core\src\message_history.rs:
core\src\model_provider_info.rs:
core\src\models.rs:
core\src\openai_api_key.rs:
core\src\openai_tools.rs:
core\src\project_doc.rs:
core\src\protocol.rs:
core\src\rollout.rs:
core\src\safety.rs:
core\src\user_notification.rs:
core\src\util.rs:
core\src\../prompt.md:
core\src\seatbelt_base_policy.sbpl:

# env-dep:CARGO_PKG_VERSION=0.0.0
