{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 8474796055553262012, "deps": [[1573238666360410412, "rand_chacha", false, 14748887965988906248], [18130209639506977569, "rand_core", false, 13068229328663427959]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-dd74d9f9774e220c\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}